@extends('layouts.app')

@section('title', '<PERSON><PERSON> - <PERSON><PERSON><PERSON>golahan Wilkerstat SE2026')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/training-modern.css') }}">
@endpush

@section('content')
<!-- Modern Page Header -->
<div class="modern-page-header">
    <div class="container">
        <div class="modern-page-header-content">
            <div class="modern-page-title-section">
                <h1 class="modern-page-title">
                    <i class="fas fa-book-open"></i>
                    <PERSON><PERSON>ela<PERSON>han
                </h1>
                <p class="modern-page-subtitle">
                    Koleksi lengkap materi pelatihan Pengolahan Wilkerstat SE2026
                </p>
                <p class="modern-page-description">
                    Akses semua materi pembelajaran yang terorganisir berdasarkan modul, mulai dari dasar hingga lanjutan. Tersedia dalam format PDF dan dilengkapi dengan panduan interaktif.
                </p>
            </div>
            <div class="modern-page-actions">
                <a href="{{ $googleDriveLink }}" class="modern-btn modern-btn-primary" target="_blank">
                    <i class="fab fa-google-drive"></i>
                    <span>Koleksi di Google Drive</span>
                </a>
                <button class="modern-btn modern-btn-secondary" onclick="downloadAll()">
                    <i class="fas fa-download"></i>
                    <span>Unduh Semua</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modern Overview Stats -->
<div class="container">
    <div class="modern-stats-grid">
        <div class="modern-stat-card">
            <div class="modern-stat-icon modern-stat-icon-primary">
                <i class="fas fa-layer-group"></i>
            </div>
            <div class="modern-stat-content">
                <div class="modern-stat-number">{{ $totalCategories }}</div>
                <div class="modern-stat-label">Kategori Materi</div>
            </div>
        </div>

        <div class="modern-stat-card">
            <div class="modern-stat-icon modern-stat-icon-success">
                <i class="fas fa-file-pdf"></i>
            </div>
            <div class="modern-stat-content">
                <div class="modern-stat-number">{{ $availableFilesCount }}</div>
                <div class="modern-stat-label">Dokumen PDF</div>
            </div>
        </div>

        <div class="modern-stat-card">
            <div class="modern-stat-icon modern-stat-icon-info">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="modern-stat-content">
                <div class="modern-stat-number">{{ $totalMaterials }}</div>
                <div class="modern-stat-label">Total Materi</div>
            </div>
        </div>

        <div class="modern-stat-card">
            <div class="modern-stat-icon modern-stat-icon-warning">
                <i class="fab fa-google-drive"></i>
            </div>
            <div class="modern-stat-content">
                <div class="modern-stat-number">Online</div>
                <div class="modern-stat-label">Akses Google Drive</div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Search and Filter Section -->
<div class="container">
    <div class="modern-search-section">
        <div class="modern-search-card">
            <div class="modern-search-header">
                <h3 class="modern-search-title">
                    <i class="fas fa-search"></i>
                    Cari Materi Pelatihan
                </h3>
                <p class="modern-search-subtitle">Temukan materi yang Anda butuhkan dengan mudah</p>
            </div>
            <div class="modern-search-controls">
                <div class="modern-search-input-group">
                    <i class="fas fa-search modern-search-icon"></i>
                    <input type="text" id="searchInput" class="modern-search-input" placeholder="Cari berdasarkan judul, deskripsi, atau kategori...">
                </div>
                <div class="modern-filter-group">
                    <select id="typeFilter" class="modern-filter-select">
                        <option value="">Semua Tipe</option>
                        <option value="PDF">PDF</option>
                        <option value="Video">Video</option>
                    </select>
                </div>
                <div class="modern-filter-group">
                    <button class="modern-btn modern-btn-outline" onclick="resetSearch()">
                        <i class="fas fa-refresh"></i>
                        <span>Reset</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Materials Section -->
<div class="container">
    <div class="modern-materials-section">
        @foreach($materials as $moduleName => $moduleItems)
        <div class="modern-module-section" data-module="{{ strtolower($moduleName) }}">
            <div class="modern-module-header">
                <div class="modern-module-title-section">
                    <h2 class="modern-module-title">
                        @if($moduleName === 'Buku Pedoman Lengkap')
                            <i class="fas fa-book"></i>
                        @elseif($moduleName === 'Materi Dasar Pengolahan')
                            <i class="fas fa-graduation-cap"></i>
                        @elseif($moduleName === 'Mekanisme dan Prosedur')
                            <i class="fas fa-cogs"></i>
                        @elseif($moduleName === 'Sistem dan Aplikasi')
                            <i class="fas fa-desktop"></i>
                        @elseif($moduleName === 'Pengolahan Peta dan Geospasial')
                            <i class="fas fa-map"></i>
                        @else
                            <i class="fas fa-folder-open"></i>
                        @endif
                        {{ $moduleName }}
                    </h2>
                    <div class="modern-module-stats">
                        <span class="modern-module-count">{{ count($moduleItems) }} item</span>
                        <div class="modern-module-types">
                            @php
                                $types = collect($moduleItems)->pluck('type')->unique();
                            @endphp
                            @foreach($types as $type)
                                <span class="modern-type-badge modern-type-{{ strtolower($type) }}">{{ $type }}</span>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="modern-module-actions">
                    <button class="modern-btn modern-btn-secondary modern-btn-sm" onclick="downloadModule('{{ $moduleName }}')">
                        <i class="fas fa-download"></i>
                        <span>Unduh Modul</span>
                    </button>
                    <button class="modern-btn modern-btn-outline modern-btn-sm" onclick="toggleModule('{{ $loop->index }}')">
                        <i class="fas fa-chevron-down modern-toggle-icon" id="toggle-{{ $loop->index }}"></i>
                    </button>
                </div>
            </div>

            <div class="modern-module-content" id="module-{{ $loop->index }}">
                <div class="modern-materials-grid">
                @foreach($moduleItems as $item)
                <div class="modern-material-card @if(isset($item['is_complete_manual']) && $item['is_complete_manual']) modern-material-card-featured @endif"
                     data-title="{{ strtolower($item['title']) }}"
                     data-description="{{ strtolower($item['description']) }}"
                     data-type="{{ $item['type'] }}">

                    @if(isset($item['is_complete_manual']) && $item['is_complete_manual'])
                        <div class="modern-material-featured-badge">
                            <i class="fas fa-star"></i>
                            <span>Buku Lengkap</span>
                        </div>
                    @endif

                    <div class="modern-material-header">
                        <div class="modern-material-type-badge modern-type-{{ strtolower($item['type']) }}">
                            @if($item['type'] === 'PDF')
                                <i class="fas fa-file-pdf"></i>
                            @elseif($item['type'] === 'Video')
                                <i class="fas fa-video"></i>
                            @else
                                <i class="fas fa-file"></i>
                            @endif
                            {{ $item['type'] }}
                        </div>
                        <div class="modern-material-actions">
                            <button class="modern-btn-action modern-btn-favorite" onclick="toggleFavorite(this)" title="Tandai Favorit">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="modern-material-content">
                        <h3 class="modern-material-title">{{ $item['title'] }}</h3>
                        <p class="modern-material-description">{{ $item['description'] }}</p>

                        <div class="modern-material-meta">
                            @if(isset($item['size']))
                                <div class="modern-meta-item">
                                    <i class="fas fa-file-alt"></i>
                                    <span>{{ $item['size'] }}</span>
                                </div>
                            @endif
                            @if(isset($item['duration']))
                                <div class="modern-meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ $item['duration'] }}</span>
                                </div>
                            @endif
                            @if(isset($item['difficulty']))
                                <div class="modern-meta-item modern-difficulty-{{ strtolower(str_replace(' ', '-', $item['difficulty'])) }}">
                                    <i class="fas fa-signal"></i>
                                    <span>{{ $item['difficulty'] }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="modern-material-footer">
                        <div class="modern-material-actions-primary">
                            @if(isset($item['detailed_link']))
                                <a href="{{ $item['detailed_link'] }}" class="modern-btn modern-btn-primary modern-btn-block">
                                    <i class="fas fa-book-open"></i>
                                    <span>Baca Materi Lengkap</span>
                                </a>
                            @else
                                <a href="{{ $item['link'] }}" class="modern-btn modern-btn-primary modern-btn-block" target="_blank" onclick="trackDownload('{{ $item['title'] }}')">
                                    @if($item['type'] === 'Video')
                                        <i class="fas fa-play"></i>
                                        <span>Tonton Video</span>
                                    @else
                                        <i class="fas fa-download"></i>
                                        <span>Unduh {{ $item['type'] }}</span>
                                    @endif
                                </a>
                            @endif
                        </div>
                        <div class="modern-material-actions-secondary">
                            @if(isset($item['google_drive_link']))
                                <a href="{{ $item['google_drive_link'] }}" class="modern-btn modern-btn-outline modern-btn-sm" target="_blank">
                                    <i class="fab fa-google-drive"></i>
                                    <span>View in Drive</span>
                                </a>
                            @else
                                <a href="{{ $googleDriveLink }}" class="modern-btn modern-btn-outline modern-btn-sm" target="_blank">
                                    <i class="fab fa-google-drive"></i>
                                    <span>View in Drive</span>
                                </a>
                            @endif
                            <button class="modern-btn modern-btn-outline modern-btn-sm" onclick="previewMaterial('{{ $item['title'] }}', '{{ $item['description'] }}', '{{ $item['type'] }}')">
                                <i class="fas fa-eye"></i>
                                <span>Preview</span>
                            </button>
                        </div>
                    </div>
                </div>
                @endforeach
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>

<!-- Modern No Results -->
<div class="modern-no-results" id="noResults" style="display: none;">
    <div class="modern-no-results-content">
        <div class="modern-no-results-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3 class="modern-no-results-title">Tidak ada materi ditemukan</h3>
        <p class="modern-no-results-description">Coba ubah kata kunci pencarian atau filter Anda</p>
        <button class="modern-btn modern-btn-primary" onclick="resetSearch()">
            <i class="fas fa-refresh"></i>
            <span>Reset Pencarian</span>
        </button>
    </div>
</div>

<!-- Modern Preview Modal -->
<div class="modern-modal" id="previewModal">
    <div class="modern-modal-overlay" onclick="closePreview()"></div>
    <div class="modern-modal-content modern-modal-large">
        <div class="modern-modal-header">
            <h3 class="modern-modal-title" id="previewTitle">Preview Materi</h3>
            <button class="modern-modal-close" onclick="closePreview()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modern-modal-body" id="previewBody">
            <!-- Content will be populated by JavaScript -->
        </div>
        <div class="modern-modal-footer">
            <button class="modern-btn modern-btn-secondary" onclick="closePreview()">
                <i class="fas fa-times"></i>
                <span>Tutup</span>
            </button>
            <button class="modern-btn modern-btn-primary" id="previewDownload">
                <i class="fas fa-external-link-alt"></i>
                <span>Buka Materi</span>
            </button>
        </div>
    </div>
</div>

<!-- Modern Quick Access Sidebar -->
<div class="modern-quick-access" id="quickAccess">
    <div class="modern-quick-access-header">
        <h4 class="modern-quick-access-title">Akses Cepat</h4>
        <button class="modern-quick-access-toggle" onclick="toggleQuickAccess()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    <div class="modern-quick-access-content">
        <div class="modern-quick-section">
            <h5 class="modern-quick-section-title">Favorit</h5>
            <div id="favoritesList" class="modern-quick-list">
                <p class="modern-empty-state">Belum ada materi favorit</p>
            </div>
        </div>
        <div class="modern-quick-section">
            <h5 class="modern-quick-section-title">Terakhir Diakses</h5>
            <div id="recentList" class="modern-quick-list">
                <p class="modern-empty-state">Belum ada aktivitas</p>
            </div>
        </div>
    </div>
</div>
@endsection

<!-- Old styles removed - using modern framework -->



@push('scripts')
<script>
    let favorites = JSON.parse(localStorage.getItem('materialFavorites') || '[]');
    let recentAccess = JSON.parse(localStorage.getItem('materialRecent') || '[]');

    // Search and filter functionality
    function filterMaterials() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        
        const materialCards = document.querySelectorAll('.material-card');
        const moduleSection = document.querySelectorAll('.module-section');
        
        let hasVisibleResults = false;
        
        moduleSection.forEach(module => {
            let hasVisibleCards = false;
            const cards = module.querySelectorAll('.material-card');
            
            cards.forEach(card => {
                const title = card.dataset.title;
                const description = card.dataset.description;
                const type = card.dataset.type;
                
                const matchesSearch = !searchTerm || title.includes(searchTerm) || description.includes(searchTerm);
                const matchesType = !typeFilter || type === typeFilter;
                
                if (matchesSearch && matchesType) {
                    card.style.display = 'block';
                    hasVisibleCards = true;
                    hasVisibleResults = true;
                } else {
                    card.style.display = 'none';
                }
            });
            
            module.style.display = hasVisibleCards ? 'block' : 'none';
        });
        
        document.getElementById('noResults').style.display = hasVisibleResults ? 'none' : 'block';
        document.querySelector('.materials-section').style.display = hasVisibleResults ? 'block' : 'none';
    }

    // Reset search
    function resetSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        filterMaterials();
    }

    // Toggle module visibility
    function toggleModule(index) {
        const content = document.getElementById(`module-${index}`);
        const icon = document.getElementById(`toggle-${index}`);
        
        content.classList.toggle('collapsed');
        icon.classList.toggle('rotated');
    }

    // Toggle favorite
    function toggleFavorite(button) {
        const card = button.closest('.material-card');
        const title = card.querySelector('.material-title').textContent;
        const icon = button.querySelector('i');
        
        if (favorites.includes(title)) {
            favorites = favorites.filter(fav => fav !== title);
            icon.className = 'far fa-heart';
            button.classList.remove('active');
        } else {
            favorites.push(title);
            icon.className = 'fas fa-heart';
            button.classList.add('active');
        }
        
        localStorage.setItem('materialFavorites', JSON.stringify(favorites));
        updateQuickAccess();
    }

    // Track download
    function trackDownload(title) {
        // Add to recent access
        recentAccess = recentAccess.filter(item => item !== title);
        recentAccess.unshift(title);
        recentAccess = recentAccess.slice(0, 5); // Keep only last 5
        
        localStorage.setItem('materialRecent', JSON.stringify(recentAccess));
        updateQuickAccess();
    }

    // Preview material
    function previewMaterial(title, description, type) {
        document.getElementById('previewTitle').textContent = `Preview: ${title}`;
        
        const previewBody = document.getElementById('previewBody');
        previewBody.innerHTML = `
            <div class="preview-content">
                <div class="preview-header">
                    <div class="preview-type type-${type.toLowerCase()}">
                        ${type === 'PDF' ? '<i class="fas fa-file-pdf"></i>' : '<i class="fas fa-video"></i>'}
                        ${type}
                    </div>
                </div>
                <h3 class="preview-title">${title}</h3>
                <p class="preview-description">${description}</p>
                <div class="preview-info">
                    <div class="info-item">
                        <i class="fas fa-info-circle"></i>
                        <span>Materi ini disediakan melalui tautan eksternal</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Pastikan koneksi internet stabil untuk akses optimal</span>
                    </div>
                    ${type === 'Video' ? '<div class="info-item"><i class="fas fa-clock"></i><span>Durasi estimasi: 30-45 menit</span></div>' : ''}
                </div>
            </div>
            <style>
                .preview-content {
                    padding: 1rem 0;
                }
                .preview-header {
                    margin-bottom: 1rem;
                }
                .preview-type {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    color: white;
                }
                .preview-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin-bottom: 1rem;
                    color: var(--text-primary);
                }
                .preview-description {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 1.5rem;
                }
                .preview-info {
                    background: var(--bg-light);
                    padding: 1rem;
                    border-radius: 0.5rem;
                }
                .info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-bottom: 0.5rem;
                    font-size: 0.875rem;
                    color: var(--text-secondary);
                }
                .info-item:last-child {
                    margin-bottom: 0;
                }
                .info-item i {
                    color: var(--primary-color);
                }
            </style>
        `;
        
        document.getElementById('previewModal').style.display = 'block';
    }

    // Close preview
    function closePreview() {
        document.getElementById('previewModal').style.display = 'none';
    }

    // Download module
    function downloadModule(moduleName) {
        alert(`Mengunduh semua materi dari ${moduleName}...\nFitur ini akan mengarahkan ke halaman unduhan batch.`);
    }

    // Download all materials
    function downloadAll() {
        alert('Mengunduh semua materi pelatihan...\nFitur ini akan mengarahkan ke halaman unduhan lengkap.');
    }

    // Toggle quick access
    function toggleQuickAccess() {
        const quickAccess = document.getElementById('quickAccess');
        quickAccess.classList.toggle('open');
        
        const icon = document.querySelector('.quick-access-toggle i');
        if (quickAccess.classList.contains('open')) {
            icon.className = 'fas fa-chevron-left';
        } else {
            icon.className = 'fas fa-chevron-right';
        }
    }

    // Update quick access content
    function updateQuickAccess() {
        // Update favorites
        const favoritesList = document.getElementById('favoritesList');
        if (favorites.length === 0) {
            favoritesList.innerHTML = '<p class="empty-state">Belum ada materi favorit</p>';
        } else {
            favoritesList.innerHTML = favorites.map(fav => 
                `<div class="quick-item">${fav}</div>`
            ).join('');
        }
        
        // Update recent access
        const recentList = document.getElementById('recentList');
        if (recentAccess.length === 0) {
            recentList.innerHTML = '<p class="empty-state">Belum ada aktivitas</p>';
        } else {
            recentList.innerHTML = recentAccess.map(item => 
                `<div class="quick-item">${item}</div>`
            ).join('');
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Search event listeners
        document.getElementById('searchInput').addEventListener('input', filterMaterials);
        document.getElementById('typeFilter').addEventListener('change', filterMaterials);
        
        // Initialize favorites
        favorites.forEach(title => {
            const cards = document.querySelectorAll('.material-card');
            cards.forEach(card => {
                const cardTitle = card.querySelector('.material-title').textContent;
                if (cardTitle === title) {
                    const button = card.querySelector('.btn-favorite');
                    const icon = button.querySelector('i');
                    icon.className = 'fas fa-heart';
                    button.classList.add('active');
                }
            });
        });
        
        // Update quick access
        updateQuickAccess();
        
        // Modal close on outside click
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
    });
</script>
@endpush